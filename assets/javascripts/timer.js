let progressCircle = ""
let progressCircleMob=""
let radius=0
let circumference=0
var displayOutput = document.querySelector('.display-remain-time');
var pauseBtn = document.getElementById('pause');
var setterBtns = document.querySelectorAll('button[data-setter]');
function getTimerDt(){
    progressCircle = document.querySelector('#time-progress circle:nth-child(2)');
    progressCircleMob = document.querySelector('#time-progressMob circle:nth-child(2)');
    if(progressCircle==null){
        progressCircle = progressCircleMob
    }
    // Only proceed if we found a timer element
    if(progressCircle){
        radius = progressCircle.r.baseVal.value;
        circumference = 2 * Math.PI * radius;
        progressCircle.style.strokeDasharray = circumference
        progressCircle.style.strokeDashoffset = circumference
    }
    displayOutput = document.querySelector('.display-remain-time');
    pauseBtn = document.getElementById('pause');
    setterBtns = document.querySelectorAll('button[data-setter]');
}


function update(value, timePercent) {
    if(progressCircle){
        const offset1 = circumference - (value / wholeTime) * circumference;
        progressCircle.style.strokeDashoffset = offset1;
    }else{
        progressCircle = document.querySelector('#time-progress circle:nth-child(2)')
        if(!progressCircle){
            progressCircle = document.querySelector('#time-progress g:nth-of-type(2) circle');
        }
        // If desktop timer not found, try mobile timer (same logic as getTimerDt)
        if(!progressCircle){
            progressCircle = document.querySelector('#time-progressMob circle:nth-child(2)');
        }
        // Only proceed if we found a timer element
        if(progressCircle){
            radius = progressCircle.r.baseVal.value;
            circumference = 2 * Math.PI * radius;
            const offset1 = circumference - (value / wholeTime) * circumference;
            progressCircle.style.strokeDashoffset = offset1;
        }
    }
};

//circle ends


var intervalTimer;
var timeLeft;
var wholeTime = 3600; // manage this to set the whole time
var isPaused = false;
var isStarted = false;


function changeWholeTime(seconds){
    wholeTime=seconds;
    update(wholeTime,wholeTime);
    displayTimeLeft(wholeTime);
}



function timer (seconds){ //counts time, takes seconds
    var remainTime = Date.now() + (seconds * 1000);
    displayTimeLeft(seconds);
    intervalTimer = setInterval(function(){
        timeLeft = Math.round((remainTime - Date.now()) / 1000) - 1;
        if(timeLeft < 0){
            clearInterval(intervalTimer);
            isStarted = false;
            setterBtns.forEach(function(btn){
                btn.disabled = false;
                btn.style.opacity = 1;
            });
            displayTimeLeft(wholeTime);
            pauseBtn.classList.remove('pause');
            pauseBtn.classList.add('play');
            timeCompleted();
            return ;
        }
        displayTimeLeft(timeLeft);
    }, 1000);
}
function pauseTimer(event){
    if(isStarted === false){
        timer(wholeTime);
        isStarted = true;
        this.classList.remove('play');
        this.classList.add('pause');

        setterBtns.forEach(function(btn){
            btn.disabled = true;
            btn.style.opacity = 0.5;
        });

    }else if(isPaused){
        this.classList.remove('play');
        this.classList.add('pause');
        timer(timeLeft);
        isPaused = isPaused ? false : true ;
    }else{
        this.classList.remove('pause');
        this.classList.add('play');
        clearInterval(intervalTimer);
        isPaused = isPaused ? false : true ;
    }
}
var secTimer;
function displayTimeLeft (timeLeft){//displays time on the input
    secTimer=timeLeft;
    var minutes = Math.floor(timeLeft / 60);
    var seconds = timeLeft % 60;
    var hours = Math.floor(minutes / 60) ;
    minutes %= 60;
    hours %= 60;
    var displayString ="";
    if(hours<10) displayString="0"+hours+":";
    else displayString=""+hours+":";

    if(minutes<10) displayString +="0"+minutes+":";
    else displayString +=""+minutes+":";

    if(seconds<10) displayString +="0"+seconds;
    else displayString +=""+seconds;
    displayOutput.textContent = displayString;
    setterBtns.forEach(function(btn){
        btn.disabled = false;
        btn.style.opacity = 1;
    });
    update(timeLeft, wholeTime);
}

function startTimer(){
    timer(wholeTime);
    isStarted = true;
    pauseBtn.classList.remove('play');
    pauseBtn.classList.add('pause');
    Object.keys(setterBtns).forEach(function(btn){
        btn.disabled = true;
    });
}

function startTimerForTest(){
    clearInterval(intervalTimer);
    timer(wholeTime);
    isStarted = true;
    pauseBtn.classList.remove('play');
    pauseBtn.classList.add('pause');
    Object.keys(setterBtns).forEach(function(btn){
        btn.disabled = true;
    });
}

if(pauseBtn){
    pauseBtn.addEventListener('click',pauseTimer);
}


function timeCompleted(){
    if(!testSubmitted) {
        if (quizMode=='testSeries' && secpresent) {
            var sectionsList = document.getElementById("sectionSelect");
            if (sectionsList.selectedIndex < (sectionsList.length - 1)) {
                document.getElementById('comingup-subject').innerHTML = sectionsList[sectionsList.selectedIndex + 1].value;
                continueTest();
            } else {
                forceSubmitTest();
            }
        }else if(quizMode=='testSeries'){
            forceSubmitTest();
        }
    }
}