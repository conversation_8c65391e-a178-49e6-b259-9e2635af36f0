//user Answers in Result Page
var skipperScore;
var correctScore;
var wrongScore;
var yt_url;
var answerByUser;
var qaAnswers;
var secpresent=false;
var secWiseCorrect;
var secWiseInCorrect;
var secWiseSkipped;
var secWiseAttempted;
var totalSectionQ=[];
var secWiseTotal;
var resultSectionSelectValue;
var mergedUserAnswers;
var sectionList;
var testSubmitted=false;
function resultSectionChange(){
    resultSectionSelectValue = document.getElementById("sectionSelectDropDown").value;
    quizChart.destroy();
    showAllAnswers();
}

function showAllAnswers(){
   var sectionResultQuestions=[]
    var allQuestions = questions;
    var sectionCorrectMarks=-1;
    var sectionIncorrectMarks=0;
    answerByUser=JSON.parse(qaObj);
    qaAnswers=answerByUser.userAnswers;
    if (quizChart!=undefined){
        quizChart.destroy();
    }
    if (storingInitialData.examDtl ==null){
        //no nothing
    }else{
        if (storingInitialData.examDtl != "" && storingInitialData.examDtl.length > 0 && storingInitialData.examDtl != "" && storingInitialData.examDtl !=null ) {
            $(".section-result").show();
            secpresent=true;
            $("#sectionSelectOption").show();
            resultSectionSelectValue   = document.getElementById("sectionSelectDropDown").value;
            for (var m=0;m<questions.length;m++){
                if (questions[m].subject == resultSectionSelectValue){
                    sectionResultQuestions.push(questions[m])
                }

            }
            var examDtl =  JSON.parse(storingInitialData.examDtl);
            for(var s=0;s<examDtl.length;s++){
                //add correct and incorrect marks to each question

                for (var m=0;m<questions.length;m++){
                    if (questions[m].subject ==examDtl[s].subject){
                        if (typeof examDtl[s].rightAnswerMarks !== 'undefined'&&examDtl[s].rightAnswerMarks!=null&&examDtl[s].rightAnswerMarks!="null") {
                            questions[m].marks = Number(examDtl[s].rightAnswerMarks);
                            if (typeof examDtl[s].wrongAnswerMarks !== 'undefined'&&examDtl[s].wrongAnswerMarks!=null&&examDtl[s].wrongAnswerMarks!="null") {
                                questions[m].negativeMarks = Number(examDtl[s].wrongAnswerMarks);
                            }
                        }
                    }
                }
                if(examDtl[s].subject==resultSectionSelectValue){
                    if (typeof examDtl[s].rightAnswerMarks !== 'undefined'&&examDtl[s].rightAnswerMarks!=null&&examDtl[s].rightAnswerMarks!="null") {
                       sectionCorrectMarks = Number(examDtl[s].rightAnswerMarks);
                        if (typeof examDtl[s].wrongAnswerMarks !== 'undefined'&&examDtl[s].wrongAnswerMarks!=null&&examDtl[s].wrongAnswerMarks!="null") {
                            sectionIncorrectMarks = Number(examDtl[s].wrongAnswerMarks);
                        }
                    }
                }
            }
        }
    }

    var list = allQuestions.map(t1 => ({...t1, ...qaAnswers.find(t2 => t2.id == t1.id)}))
    mergedUserAnswers = list;

    var htmlStr='';
    var correctArr=[];
    var inCorrectArr=[];
    var skipArr=[];
    var secCrtAns=[];
    var secInAns=[];
    var secSpAns=[];
    var attemptedQs=[];
    var ttCount=[];
    var crtGA=[];
    var incGA=[];
    var skpGA=[];
    htmlStr +="<div class='container'>";
    var score=0;
    var showScore = false;
    var secScore = 0;
    for(var i=0;i<mergedUserAnswers.length;i++) {
        var ques = mergedUserAnswers[i].ps;
        ques=replaceSymbols(ques);
        ques=checkLanguageAndImage(ques);
        ques=replaceImageUrlForApps(ques);

        var directions=mergedUserAnswers[i].directions;

        if(directions !=null) {
            directions=replaceSymbols(directions);
            directions=checkLanguageAndImage(directions);
            directions=replaceImageUrlForApps(directions);
            $('.directions').removeClass('d-none');

        }
        var option1 = mergedUserAnswers[i].op1;
        option1=replaceSymbols(option1);
        option1=checkLanguageAndImage(option1);
        option1=replaceImageUrlForApps(option1);
        var option2 = mergedUserAnswers[i].op2;
        option2=replaceSymbols(option2);
        option2=checkLanguageAndImage(option2);
        option2=replaceImageUrlForApps(option2);
        var option3 = mergedUserAnswers[i].op3;
        option3=replaceSymbols(option3);
        option3=checkLanguageAndImage(option3);
        option3=replaceImageUrlForApps(option3);
        var option4 = mergedUserAnswers[i].op4;
        option4=replaceSymbols(option4);
        option4=checkLanguageAndImage(option4);
        option4=replaceImageUrlForApps(option4);
        var option5=mergedUserAnswers[i].op5;
            option5=replaceSymbols(option5);
            option5=checkLanguageAndImage(option5);
            option5=replaceImageUrlForApps(option5);

        var explanation='';
        var explanationLink=mergedUserAnswers[i].explainLink;
        if(mergedUserAnswers[i].answerDescription != ''){
            explanation=replaceSymbols(mergedUserAnswers[i].answerDescription);
            explanation=checkLanguageAndImage(explanation);
            explanation=replaceImageUrlForApps(explanation);
        }

        if (mergedUserAnswers[i].explainLink!= '' && mergedUserAnswers[i].explainLink != 'null' && mergedUserAnswers[i].explainLink != null) {
            $('.show-video-explanation').show();
            if(explanationLink.includes("/")){
                yt_url=mergedUserAnswers[i].explainLink;
            }else{
                yt_url = "https://www.youtube.com/embed/" + explanationLink;
            }
            $('#videoExplanation').html('<iframe class="video_iframe" src="'+yt_url+'" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>');
        } else {
            $('.show-video-explanation').hide();
        }
        htmlStr += "<div class='mcq-answers'>" ;
            htmlStr += "<div class='d-flex justify-content-end align-items-center' style='gap: 5px'>";
            if (mergedUserAnswers[i].favouriteQ == "true"){
                htmlStr += "<i class='fa-solid fa-star favStarBtn-1 addedToFav' id='favRes-"+mergedUserAnswers[i].id+"' data-index="+i+"></i>"+
                    '<span class="tooltiptext tooltiptext-1">Mark as Unfavourite MCQ</span>';
            }else if (mergedUserAnswers[i].favouriteQ == "false"){
                htmlStr +=  "<i class='fa-regular fa-star favStarBtn-1' id='favRes-"+mergedUserAnswers[i].id+"' data-index="+i+"></i>"+
                    '<span class="tooltiptext tooltiptext-1">Mark as favourite MCQ</span>';
            }
            htmlStr +=  "<a href='javascript:showReportModal("+(mergedUserAnswers[i].id)+")'class='reporticon d-flex justify-content-end'>Report</a>";
            htmlStr +="</div>";
        htmlStr +="<div id='que-no'>Q" + (i + 1) + ".</div>" +
            "<div class='question-wrapper' id='quz-" + i + "'>" ;
        if(mergedUserAnswers[i].directions !=null) {
            htmlStr +=   "<div class='directions'>" +
                "                                <p id='direction' class='more'>" + directions+ "</p>" +
                "                        </div>";
        }
        htmlStr +=  "<p class='que_text'>" + ques + "</p>" +
            "</div>" +
            "<div class='que-options-wrapper mt-4'>" +
            "<div class='que-options' id='que-" + i + "'>" +
            "<div id=ans-" + i + "' class='option-qa'>";
        if (mergedUserAnswers[i].ans1 == 'Yes') {
            htmlStr += '<div class="option correct">';
        } else if ((mergedUserAnswers[i].ans1 != 'Yes') && (mergedUserAnswers[i].userOption == '1')) {
            htmlStr += '<div class="option incorrect">';
        } else {
            htmlStr += '<div class="option">';
        }
        htmlStr += '<span>' + option1 + '</span>' +
            '</div>' +
            "</div>" +
            "<div id=ans-" + i + "' class='option-qa'>";
        if (mergedUserAnswers[i].ans2 == 'Yes') {
            htmlStr += '<div class="option correct">';
        } else if ((mergedUserAnswers[i].ans2 != 'Yes') && (mergedUserAnswers[i].userOption == '2')) {
            htmlStr += '<div class="option incorrect">';
        } else {
            htmlStr += '<div class="option">';
        }
        htmlStr += '<span>' + option2 + '</span></div>' +
            "</div>" ;

            if(mergedUserAnswers[i].op3!=null&&mergedUserAnswers[i].op3!='') {
                "<div id=ans-" + i + "' class='option-qa'>";
                if (mergedUserAnswers[i].ans3 == 'Yes') {
                    htmlStr += '<div class="option correct">';
                } else if ((mergedUserAnswers[i].ans3 != 'Yes') && (mergedUserAnswers[i].userOption == '3')) {
                    htmlStr += '<div class="option incorrect">';
                } else {
                    htmlStr += '<div class="option">';
                }
                htmlStr += '<span>' + option3 + '</span>' +
                    '</div>' +
                    "</div>";
            }

        if(mergedUserAnswers[i].op4!=null&&mergedUserAnswers[i].op4!='') {
            htmlStr += "<div id=ans-" + i + "' class='option-qa'>";
            if (mergedUserAnswers[i].ans4 == 'Yes') {
                htmlStr += '<div class="option correct">';
            } else if ((mergedUserAnswers[i].ans4 != 'Yes') && (mergedUserAnswers[i].userOption == '4')) {
                htmlStr += '<div class="option incorrect">';
            } else {
                htmlStr += '<div class="option">';
            }
            htmlStr += '<span>' + option4 + '</span></div>' +
                "</div>";
        }

        if(mergedUserAnswers[i].op5!=null&&mergedUserAnswers[i].op5!='') {
            htmlStr += "<div id=ans-" + i + "' class='option-qa'>";
            if (mergedUserAnswers[i].ans5 == 'Yes') {
                htmlStr += '<div class="option correct">';
            } else if ((mergedUserAnswers[i].ans5 != 'Yes') && (mergedUserAnswers[i].userOption == '5')) {
                htmlStr += '<div class="option incorrect">';
            } else {
                htmlStr += '<div class="option">';
            }
            htmlStr += '<span>' + option5 + '</span></div>' +
                "</div>";
        }
        //for statistics if pressent
        if(quizStatisticsList.length>0){
            htmlStr +=  '<div class="mt-4 explanation-wrappers"><h3 class="head-text" style="margin-bottom: 2px;">Question Analytics:</h3><table border="1" cellpadding="3" cellspacing="3">' +
                '<tr style="background: #FFF0F5;font-weight: bold">' +
                '<td>Attempted</td>' +
                '<td>Solved Correctly</td>' +
                '<td>Solved Incorrectly</td>' +
                '<td>Accuracy</td>' +
                '<td>Your time</td>' +
                '<td>Average solving time</td>' +
                '<td>Fastest solving time</td>' +
                '</tr>';
            for(var s=0;s<quizStatisticsList.length;s++){
                if(quizStatisticsList[s].objId==mergedUserAnswers[i].id){
                    htmlStr +="<tr>"+
                        "<td align='center'>"+(Number(quizStatisticsList[s].correctAnswers)+Number(quizStatisticsList[s].incorrectAnswers)+Number(quizStatisticsList[s].skippedAnswers))+"</td>"+
                        "<td align='center'>"+quizStatisticsList[s].correctAnswers+"</td>"+
                        "<td align='center'>"+quizStatisticsList[s].incorrectAnswers+"</td>"+
                        "<td align='center'>"+quizStatisticsList[s].accuracy.toFixed(2)+"%</td>"+
                        "<td align='center'>"+mergedUserAnswers[i].userTime+" SECS</td>"+
                        "<td align='center'>"+quizStatisticsList[s].averageTimeTaken.toFixed(2)+" SECS</td>"+
                        "<td align='center'>"+(""+quizStatisticsList[s].fastestTime!="null"?quizStatisticsList[s].fastestTime.toFixed(2)+" SECS":"")+" </td>"+
                        "</tr>";
                }
            }
            htmlStr+="</table></div>";
        }
        if((explanation !=null)&&(explanation !='')){
            htmlStr +=  '<div class="mt-4 explanation-wrappers"><h3 class="head-text">Explanation:</h3><p class="explanation-text">' + explanation + '</p></div>';
        }
            htmlStr += '<div class="mt-4 video__explanation-wrappers d-flex justify-content-center align-items-center" id="videoExplanation" style="gap: 1rem">';
        htmlStr += '<div class="mcqChatBtns d-flex align-items-center justify-content-center mt-3 w-100">';
            if((explanationLink !=null)&&(explanationLink !='')) {
                htmlStr += '<h3 class="head-text mb-0" onclick=\'showVideoResult("' + i + '")\'><i class="material-icons mr-2">ondemand_video</i>Video Explanation</h3>';
            }
            if(gpt == "true"){
                htmlStr += '<button class="btn d-flex align-items-center mcqChatEx hin mc-primary" onclick="askDoubt(\'hint\', ' + mergedUserAnswers[i].id + ')">' +
                    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>' +
                    ' Give Hint' +
                    '</button>' +
                    '<button class="btn d-flex align-items-center mcqChatEx exp mc-secondary" onclick="askDoubt(\'explain\', ' + mergedUserAnswers[i].id + ')">' +
                    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' +
                    ' Explain MCQ' +
                    '</button>' +
                    '<button class="btn d-flex align-items-center mcqChatEx squ mc-tertiary" onclick="askDoubt(\'similarMCQ\', ' + mergedUserAnswers[i].id + ')">' +
                    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>' +
                    ' Create similar MCQs' +
                    '</button>' +
                    '</div>';
            }


        htmlStr +=  '</div>';
        if((explanationLink !=null)&&(explanationLink !='')) {
            htmlStr += '<div class=\'video-explanation-wrapper video-explanation-wrapper-' + i + '\'>\n' +
                            '<div class="explanation-header">\n' +
                                '<div class="d-flex justify-content-between">\n' +
                                    '<h4>Video Explanation:</h4>\n' +
                                    '<i class="material-icons" onclick=\'closeVideoExplanation("' + i + '")\'>close</i>\n' +
                                '</div>\n' +
                            '</div>\n' +
                            '<div class="answer-explanation">' +
                                '<div id="videoExplanation-"+i>' +
                                    '<iframe class="video_iframe" src="' + yt_url + '" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" style="width: 100%;height: 300px" allowfullscreen></iframe>' +
                                '</div>\n' +
                            '</div>\n' +
                        '</div>';
        }
        htmlStr +=  "</div>" +
            "</div>" +
            "</div>";

        if (typeof mergedUserAnswers[i].marks !== 'undefined'&&mergedUserAnswers[i].marks!=null&&mergedUserAnswers[i].marks!="null") {
            showScore = true;
        }
            if (mergedUserAnswers[i].userOption == mergedUserAnswers[i].correctOption) {
                correctArr.push(mergedUserAnswers[i].userOption);
                if (typeof mergedUserAnswers[i].marks !== 'undefined'&&mergedUserAnswers[i].marks!=null&&mergedUserAnswers[i].marks!="null") {
                    score =Number(score)+Number(questions[i].marks);
                }
            }
            if (mergedUserAnswers[i].userOption == '-1') {
                skipArr.push(mergedUserAnswers[i].userOption);
            }
            if (mergedUserAnswers[i].userOption != '-1' && mergedUserAnswers[i].userOption != mergedUserAnswers[i].correctOption) {
                inCorrectArr.push(mergedUserAnswers[i].userOption);
                if (typeof mergedUserAnswers[i].negativeMarks !== 'undefined'&&mergedUserAnswers[i].negativeMarks!=null&&mergedUserAnswers[i].negativeMarks!="null") {
                    score =Number(score)-Number(mergedUserAnswers[i].negativeMarks);
                }
            }


        if (secpresent){
            if ((mergedUserAnswers[i].userOption == mergedUserAnswers[i].correctOption) && (mergedUserAnswers[i].subject == resultSectionSelectValue)) {
                secCrtAns.push(qaAnswers[i].subject);
                if(sectionCorrectMarks!=-1){
                    secScore =Number(secScore)+Number(sectionCorrectMarks);
                }
                else if (typeof mergedUserAnswers[i].marks !== 'undefined'&&mergedUserAnswers[i].marks!=null&&mergedUserAnswers[i].marks!="null") {
                    secScore =Number(secScore)+Number(mergedUserAnswers[i].marks);
                }
            }
            if ((mergedUserAnswers[i].userOption == '-1') && (mergedUserAnswers[i].subject == resultSectionSelectValue) ) {
                secSpAns.push(qaAnswers[i].subject);
            }
            if ((mergedUserAnswers[i].userOption != '-1' && mergedUserAnswers[i].userOption != mergedUserAnswers[i].correctOption) && (mergedUserAnswers[i].subject == resultSectionSelectValue)) {
                secInAns.push(mergedUserAnswers[i].subject);
                //first preference for section marks, if not then individual
                if(sectionCorrectMarks!=-1){
                    secScore =Number(secScore)-Number(sectionIncorrectMarks);
                }
                else if (typeof mergedUserAnswers[i].negativeMarks !== 'undefined'&&mergedUserAnswers[i].negativeMarks!=null&&mergedUserAnswers[i].negativeMarks!="null") {
                    secScore =Number(secScore)-Number(mergedUserAnswers[i].negativeMarks);
                }
            }
            if ((mergedUserAnswers[i].userOption != '-1') && (mergedUserAnswers[i].subject == resultSectionSelectValue)){
                attemptedQs.push(mergedUserAnswers[i].subject);
            }
            if (mergedUserAnswers[i].subject == resultSectionSelectValue){
                ttCount.push(mergedUserAnswers[i].subject);
            }

            if (mergedUserAnswers[i].subject == resultSectionSelectValue){
                if (mergedUserAnswers[i].userOption == mergedUserAnswers[i].correctOption) {
                    crtGA.push(mergedUserAnswers[i].userOption);
                }
                if (mergedUserAnswers[i].userOption == '-1') {
                    skpGA.push(mergedUserAnswers[i].userOption);
                }
                if (mergedUserAnswers[i].userOption != '-1' && mergedUserAnswers[i].userOption != mergedUserAnswers[i].correctOption) {
                    incGA.push(mergedUserAnswers[i].userOption);
                }
            }
        }

    }
    htmlStr += "</div>";

            skipperScore = skipArr.length;
            wrongScore = inCorrectArr.length;
            correctScore = correctArr.length;


        secWiseCorrect = secCrtAns;
        secWiseInCorrect = secInAns;
        secWiseSkipped = secSpAns;
        secWiseAttempted = attemptedQs;
        secWiseTotal = ttCount;
        $('#right').text(correctArr.length);
        $('#wrong').text(inCorrectArr.length);
        $('#skipped').text(skipArr.length);

        if(showScore){
            document.getElementById('wsResultPointsCount').innerHTML = "<span class='titleSplit'>Total Marks </span><span class='titleColon'>:</span><span>" + (score%1==0?score.toFixed(0):score.toFixed(2))  + "</span>";
            if (secpresent) {
                document.getElementById('wsSectionResultPointsCount').innerHTML = "<span class='titleSplit'>Section Marks </span><span class='titleColon'>:</span><span>" + (secScore%1==0?secScore.toFixed(0):secScore.toFixed(2))  + "</span>";
            }
        }else {
            if (secpresent) {
                document.getElementById('wsResultPointsCount').innerHTML = "Points <span>" + correctScore + "/" + sectionList.length + "</span>";
            } else {
                document.getElementById('wsResultPointsCount').innerHTML = "Points <span>" + correctScore + "/" + mergedUserAnswers.length + "</span>";
            }
        }
        if(secpresent) {
            document.getElementById('correctPoints').textContent = crtGA.length;
            document.getElementById('inCorrectPoints').textContent = incGA.length;
            document.getElementById('skippedPoints').textContent = skpGA.length;
        }else{
            document.getElementById('correctPoints').textContent = correctScore;
            document.getElementById('inCorrectPoints').textContent = wrongScore;
            document.getElementById('skippedPoints').textContent = skipperScore;
        }
        document.getElementById('all').innerHTML = htmlStr;
        drawChart();

        if (secpresent){
            showSectionResults();
        }

    if(quizMode==''){
        $('.updateScore').text(userScore);
    }
    else{
        $('.updateScore').text(correctArr.length);
    }
    addMathjax();
    userPerformance();
    var favStarIcon = document.querySelectorAll('.favStarBtn-1');

    favStarIcon.forEach(item=>{
        item.addEventListener('click',function (){
            //qaObj = JSON.parse(qaObj);
            if (item.classList.contains('addedToFav')){
                typeof qaObj != "object" ? qaObj = JSON.parse(qaObj) :"";
                addRemoveFavourites(item.id,item.dataset.index,'remove','result');
            }else{
                typeof qaObj != "object" ? qaObj = JSON.parse(qaObj) :"";
                addRemoveFavourites(item.id,item.dataset.index,'add','result');
            }
        })
    })

}

//mathjax script adding
function addMathjax(){
    MathJax = { mml: { forceReparse: true } }
    // $('head').append('<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML" >');
    $('head').append('<script src="https://polyfill.io/v3/polyfill.min.js?features=es6">')
    $('head').append('<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">')
}
$("#show-answer").click(function() {
    $('.answer-wrapper').show();
    addMathjax()
});

var sectionQuizAttempted;
var sectionQuizCorrect;
var sectionQuizIncorrect;
var sectionQuizSkipped;
var sectionTotalQuiz;

function showSectionResults() {
    var optionHtml = "";
    var sectionDetailsTable = "";
    var resultSub;
    var attemptCount;
    var correctCount;
    var inCorrectCount;
    var skipCount;
    var secTC;

        for (var s = 0; s < finalList.length; s++) {
            resultSub = finalList[s];
            var exd = finalList[s].replaceAll(" ",'');

            sectionQuizAttempted = freq(secWiseAttempted);
            sectionQuizCorrect = freq(secWiseCorrect);
            sectionQuizIncorrect = freq(secWiseInCorrect);
            sectionQuizSkipped = freq(secWiseSkipped);
            sectionTotalQuiz = freq(secWiseTotal);
            replaceKeys(sectionQuizAttempted);
            replaceKeys(sectionQuizCorrect);
            replaceKeys(sectionQuizIncorrect);
            replaceKeys(sectionQuizSkipped);
            replaceKeys(sectionTotalQuiz);

            attemptCount = sectionQuizAttempted[exd];
            correctCount = sectionQuizCorrect[exd];
            inCorrectCount = sectionQuizIncorrect[exd];
            skipCount = sectionQuizSkipped[exd];
            secTC = sectionTotalQuiz[exd];

            if (attemptCount == 'undefined' || attemptCount == undefined){
                attemptCount = 0;
            }
            if (correctCount == 'undefined' || correctCount == undefined){
                correctCount = 0;
            }
            if (inCorrectCount == 'undefined' || inCorrectCount == undefined){
                inCorrectCount = 0;
            }
            if (skipCount == 'undefined' || skipCount == undefined){
                skipCount = 0;
            }

            sectionDetailsTable += "<tr>" +
                "<th scope='col'>" + resultSub + "</th>" +
                " <th scope='col'>" +attemptCount +"/"+secTC+"</th>" +
                " <th scope='col'>" + correctCount +"/"+secTC+"</th>" +
                " <th scope='col'>" + inCorrectCount +"/"+secTC+"</th>" +
                " <th scope='col'>" + skipCount +"/"+secTC+"</th>";

        }
        document.getElementById("section-result__content").innerHTML = sectionDetailsTable;
}

function freq(nums) {
    return nums.reduce((acc, curr) => {
        acc[curr] = -~acc[curr];
        return acc;
    }, {});
}

function replaceKeys(object) {
    Object.keys(object).forEach(function (key) {
        var newKey = key.replaceAll(" ","");
        if (object[key] && typeof object[key] === 'object') {
            replaceKeys(object[key]);
        }
        if (key !== newKey) {
            object[newKey] = object[key];
            delete object[key];
        }
    });
}

var quizChart;
function drawChart(){
    const ctx = document.getElementById('myChart');
    Chart.register(ChartDataLabels);
    Chart.defaults.set('plugins.datalabels', {
        // color: '#000'
        color: '#fff',
    });
    quizChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Correct','Incorrect','Skipped'],
            datasets: [{
                data: [correctScore, wrongScore, skipperScore],
                backgroundColor: ['lightGreen','rgb(255, 99, 132)','rgb(255, 205, 86)'],
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            tooltips:{
                enabled:true
            },
            plugins: {
                legend:{
                    display: true,
                    position:'bottom',
                    color:'#000',
                    align:'start',
                    labels:{
                        usePointStyle: true,
                        pointStyle:'circle',
                        color: '#fff',
                        font: {
                            size:10,
                        },

                    }
                },

                datalabels: {
                    display: true,
                    align: 'center',
                    font: {
                        size: 9,
                    },
                    formatter: (val, ctx) => {
                        // Grab the label for this value
                        const label = ctx.chart.data.labels[ctx.dataIndex];

                        // Format the number with 2 decimal places
                        const formattedVal = Intl.NumberFormat('en-US', {
                            minimumFractionDigits: 0,
                        }).format(val);

                        // Put them together
                        return `${label}: ${formattedVal}`;
                    },
                    color: '#fff',
                    backgroundColor: '#404040',
                },
            },
        }
    });
}

function openGoogleExplanationResult(ques){
    var query = $('#quz-'+ques).text();
    var url ='http://www.google.com/search?q=' + query;
    window.open(url,'_blank');
}

function userPerformance(){
    var correctAnswerPercentage = correctScore/mergedUserAnswers.length*100;


    document.getElementById('crtCnt').textContent = correctScore;
    document.getElementById('inCrtCnt').textContent = wrongScore;
    document.getElementById('skpCnt').textContent = skipperScore;


    var inCrtCnt=0;
    var skpCnt=0;
    var crtCnt=0;

    crtCnt = document.getElementById('crtCnt').textContent;
    inCrtCnt = document.getElementById('inCrtCnt').textContent
    skpCnt = document.getElementById('skpCnt').textContent;

    if (crtCnt==0){
        document.getElementById('correctRetest').setAttribute('disabled','disabled');
    }else{
        document.getElementById('correctRetest').removeAttribute('disabled')
    }

    if (inCrtCnt==0){
        document.getElementById('inCorrectRetest').setAttribute('disabled','disabled');
    }else{
        document.getElementById('inCorrectRetest').removeAttribute('disabled')
    }

    if (skpCnt==0){
        document.getElementById('skippedRetest').setAttribute('disabled','disabled');
    }else{
        document.getElementById('skippedRetest').removeAttribute('disabled')
    }
}

function sectionListConstruct(){
    var sectionListHtml = "";
    if (storingInitialData.examDtl!=null && storingInitialData.examDtl!=""){
        document.querySelector('.sectionSelectDropDown').classList.remove('d-none')
        sectionList = JSON.parse(storingInitialData.examDtl);
        for (var i=0;i<sectionList.length;i++){
            sectionListHtml+="<option value='"+sectionList[i].subject+"'>"+sectionList[i].subject+"</option>";
        }
        document.getElementById('sectionSelectDropDown').innerHTML = sectionListHtml;
        resultSectionSelectValue = sectionList[0].subject;
    }
    $('body,html').css('background-color','#060029 !important');
    $('.ws_result-summary').css('background-color','#060029 !important');
    $('.ws_result-summary h2').addClass('text-white');
    $('#goBack').addClass('text-white');
    $('.ws_result-summary .fa-circle-xmark').addClass('text-white');
    $('#practice-summary,.reviseBtn').addClass('text-white');
}

function totalTime(totalTime){
    $('.total-time-wrapper').show();
    $('.normal-time').hide();
    if((examMst.noOfSections>=1)){
        $('.sub-header').addClass('section-header');
    }
    if((examMst.noOfSections===null)||(examMst.noOfSections>=1)) {
        document.querySelector('.tot-time-text').textContent='Section Time Left';
        changeTotalTime(totalTime);
        startTotalTimer();
    }
    else{
        document.querySelector('.total-time-wrapper').remove();
        document.querySelector('.timeLeft').textContent='Total Time Left';
    }

}

function timeCompleted(){
    if(!testSubmitted) {
        if (quizMode=='testSeries' && secpresent) {
            var sectionsList = document.getElementById("sectionSelect");
            if (sectionsList.selectedIndex < (sectionsList.length - 1)) {
               document.getElementById('comingup-subject').innerHTML = sectionsList[sectionsList.selectedIndex + 1].value;
                continueTest();
            } else {
                forceSubmitTest();
            }
        }else if(quizMode=='testSeries'){
            forceSubmitTest();
        }
    }
}

