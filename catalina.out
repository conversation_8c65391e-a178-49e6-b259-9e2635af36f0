10-Sep-2025 06:37:59.181 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version:        Apache Tomcat/8.5.13
10-Sep-2025 06:37:59.189 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Mar 27 2017 14:25:04 UTC
10-Sep-2025 06:37:59.189 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server number:         8.5.13.0
10-Sep-2025 06:37:59.189 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
10-Sep-2025 06:37:59.189 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.1.129-138.220.amzn2023.x86_64
10-Sep-2025 06:37:59.189 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
10-Sep-2025 06:37:59.191 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/lib/jvm/java-1.8.0-amazon-corretto.x86_64/jre
10-Sep-2025 06:37:59.191 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_462-b08
10-Sep-2025 06:37:59.191 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Amazon.com Inc.
10-Sep-2025 06:37:59.192 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /u01/apache-tomcat-8.5.13
10-Sep-2025 06:37:59.192 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /u01/apache-tomcat-8.5.13
10-Sep-2025 06:37:59.192 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/u01/apache-tomcat-8.5.13/conf/logging.properties
10-Sep-2025 06:37:59.193 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
10-Sep-2025 06:37:59.193 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
10-Sep-2025 06:37:59.193 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
10-Sep-2025 06:37:59.193 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/u01/apache-tomcat-8.5.13
10-Sep-2025 06:37:59.193 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/u01/apache-tomcat-8.5.13
10-Sep-2025 06:37:59.193 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/u01/apache-tomcat-8.5.13/temp
10-Sep-2025 06:37:59.194 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: /usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib
10-Sep-2025 06:37:59.364 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["ajp-nio-8009"]
10-Sep-2025 06:37:59.396 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
10-Sep-2025 06:37:59.400 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 1156 ms
10-Sep-2025 06:37:59.442 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service Catalina
10-Sep-2025 06:37:59.442 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.13
10-Sep-2025 06:37:59.462 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deploying configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml
10-Sep-2025 06:37:59.520 INFO [ws-startStop-1] org.apache.catalina.startup.ExpandWar.expand An expanded directory [/u01/apache-tomcat-8.5.13/webapps/ROOT] was found with a last modified time that did not match the associated WAR. It will be deleted.
10-Sep-2025 06:38:13.678 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_displayChapterDetails_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.683 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_footer_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.685 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelector_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.686 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelectorScript_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.688 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_loginChecker_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.689 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_materialTabs_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.690 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.691 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.692 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_new_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.693 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_new_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.695 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_notesHighlightSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.697 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_notesHighlightSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.698 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_qaSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.699 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_qaSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.700 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_quizSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.700 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_quizSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.701 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readerView_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.703 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readerView_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.704 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readingSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.704 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readingSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.706 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_reviewrating_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.707 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.708 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_reviewrating_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.709 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_revision_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.710 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_revision_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.711 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_slider_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.711 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_slider_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.712 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgenModal_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.713 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgenModal_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.714 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgeneratorscripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.715 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgeneratorscripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.716 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_uploadResourcesModal_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.716 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_uploadResourcesModal_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.717 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoPlay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.718 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoPlay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.719 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.720 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.721 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_weblinksSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.722 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_weblinksSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.722 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wonderGoaStudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.723 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wonderGoaStudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.724 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wsFooter_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.724 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wsFooter_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.725 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerInput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.726 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerInput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.726 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerProcessor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.727 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerProcessor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.728 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerMatch_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.729 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerMatch_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.729 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisharihant_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.730 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisharihant_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.731 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishauthorDetails_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.731 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishauthorDetails_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.732 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbannerManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.733 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbannerManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.733 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbook_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.734 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbook_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.738 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookdtl_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.739 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookdtl_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.739 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdeleteAccount_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.740 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdeleteAccount_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.741 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdemoHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.741 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdemoHome_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.742 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisherrorPage_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.743 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisherrorPage_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.745 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.746 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.747 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.747 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.748 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.749 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.750 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.750 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.751 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.752 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.752 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.753 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.755 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.756 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.756 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.757 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.758 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.758 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.759 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.760 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.761 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.761 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.762 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.763 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.763 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.764 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.765 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.765 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.766 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.767 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.767 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.768 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.771 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.772 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.772 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.773 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.774 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.774 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.775 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.776 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.778 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.780 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.782 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.782 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiContent_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.783 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.784 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaiBookDtl_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.784 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaiBookDtl_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.784 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaibook_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.785 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaibook_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.785 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.786 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.786 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.787 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.787 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpapercreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.788 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpapercreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.788 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpaperlist_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.789 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmockpaperlist_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.789 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.790 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.790 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.790 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.791 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqplist_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.792 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqplist_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.792 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpprint_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.793 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpprint_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.793 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpview_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.794 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpview_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.794 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.795 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.795 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.796 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.796 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.797 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.797 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.798 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.798 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.799 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.799 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.800 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.800 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.800 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.801 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.801 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.803 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.803 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.805 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.805 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.805 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.806 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.806 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.807 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.807 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.807 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.809 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.810 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.810 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.813 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/usermanagement] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.814 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussion] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.814 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/admin] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.814 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/librarybooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.815 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/institute] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.815 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/logs] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/cache] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/groups] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/toDo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/shop] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/comparison] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/WsLibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/drive] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/qp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/report] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/log] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/prepjoy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/harper] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/seo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/learn] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/information] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publish] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussions] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publiclibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/content] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/games] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/view] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.863 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/client] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/marketing] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:13.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/sqlutil] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.798 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso/products] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.813 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.816 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/folder/folderHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_printBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceContents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_security.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/mobilePdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_wsOtherResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookTestSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/notesViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_flashcardMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_relatedBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flpDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayPdfMaterial.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceChapterList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashcardHome.css] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/deleteChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashCardHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/eBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.833 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createPdfBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.833 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.833 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_mobileResourceMenu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.834 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_collectionBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.834 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_genericReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/previewChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookFeatures.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/editQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/epubReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookGPTSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/robots.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/childBookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/discountManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/searchDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/printBooksDownloadPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/videoExplanationUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/refundDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/managePublishers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/accessCodeUsage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/jwplayerLive.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/cartPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/contentModeration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/publisherDsize.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.845 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/dataRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.845 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.845 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBatchUsers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.846 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/priceList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.846 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/userBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.846 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBookExpiry.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/insertjob.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageNotifications.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/emailWhitelist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/externalOrders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/moderation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/deleteuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/paymentDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookgptPromptAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/difficultyLevelMapping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookSupport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/salesDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/appVersionManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userAccess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/unblockUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/quizissues.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.855 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notificationManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.855 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/liveTestRanks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.855 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.856 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.857 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.858 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/packageBooksReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.859 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/deleteUserBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/migrateuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.865 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/karnataka.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.867 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyCurrentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.869 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/cacscma.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.869 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/ctet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.871 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/enggentrances.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/affiliation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentaffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quizAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/neet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/eBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/history.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/joinGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/audioChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoy-loader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/join.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/creator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_quizResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.894 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/_instituteBanners.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.894 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportInstituteAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.894 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/ntse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageInstitutePage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/isbnKeyword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userLoginLimit.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.901 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportDoris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageClasses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/registeredUserReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/recentInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libraryUserUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportTitleWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/downloadUsageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportCorporateWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.905 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/instituteProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store1.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/about.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_billingShipping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/migrateBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/recharge.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/setBookType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_aiStoreLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_aiStoreDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.926 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/wrongPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.926 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/packageBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.926 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eduWonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_reportSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/schoolProducts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/sageLanding.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/books.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/description.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/shoppingCart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_moreOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signup_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtulibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/toppers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_latestQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eBooksStoreIntegration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/careercounselling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.946 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_sideBar.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_login.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_institutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_searchSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/landingPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/checkout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bannerSlider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/leaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/directSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/appLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtubook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myActivity.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/userOrderProcess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/products.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_continueLearning.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/publishersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/teachersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuadmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/studentproblems.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/pdftoepub.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/blogs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_ibookgpt-promotion.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.964 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.972 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/unmarkedQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/bookai.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/aibook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/aiBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qplist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpprint.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpview.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/mockpaperlist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/mockpapercreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/automatedVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/autoImageVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteWelcomeEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendContactUs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice2020.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ccavRequestHandler.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageupload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/radianBooksInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsinvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/winnersInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wolterskluwerSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_loggedactivities.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailArihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/edugorillaInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailLibwonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchaseEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/blackspineInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userActiveCartEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/authordetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteUserEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPaidPreviewEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/jbclassInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/paymentPendingEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/enquiryFormEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRequestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddquiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/displayChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/contentModerationEmailWS.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/mtgInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswaalInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPurchasedBookEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/affiliationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:14.999 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.000 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/editProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.000 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_addTopic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.000 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.000 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/chapterDownloadError.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_changePasswordModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/returnPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswalpublisherInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/externalPurchaseMail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddlink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/approveContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_prepjoy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_contentCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfileSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/arihantInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/privateLabelInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/prepjoyInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.007 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/purchaseConfirmationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.007 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/appInAppInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.007 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookPrice/_priceManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/deliveryCharges/manageDeliveryCharges.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_categories.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_printSearch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/activitiesUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/sendInvite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/pageManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/allWebsites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/ibookso.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPageCreation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_otpOnlyLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_ibooksoBanner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/createNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/wileySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.018 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cuetAcademics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/loginPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/knimbus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/gptsir.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/accessCodeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/addSite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/listSites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/aiStore.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileSignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_plindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wpfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/flpReportDownload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/success.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signUp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mainheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicdisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/findFriends.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcqalt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/forgotpassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/topic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groupdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/facebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mta.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_chaptersModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/tour.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wsindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/recent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicinclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/futureLearningProgram.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groups.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/careers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/termsandconditions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tof.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_fib.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_opp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signupNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_pnavheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderMCQContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/doubts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/postDetail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/members.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/reported.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/memberRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslatekids/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/toDo/toDoListAndUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/_testgen.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/monthlyQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.047 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/reportDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.047 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/leaderBoardForAdmins.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/getAllPayments.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/findScratchCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.049 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wlibrary/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.049 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/validityExtensionAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.049 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.050 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/amazonorderconfirmation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.050 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.050 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_searchScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.051 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/manageShopSpecials.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.051 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/orderManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.051 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogEnglish.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogHindi.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/addGradeInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.052 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_videoCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_blogEnglishDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/messaging/messages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.054 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/discussionBoardAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.055 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.056 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshrefund.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.057 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.057 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.057 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshprivacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.057 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshterms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.058 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/demo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.058 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.059 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/gptContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.059 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/bookChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.059 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/chat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelector.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addnotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.060 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.061 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookdetailsTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.061 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.061 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mybooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/instructorLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.062 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.063 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgenModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.063 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_materialTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.063 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/openPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/demoHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviewModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgeneratorscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.064 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_answerMatchModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferences.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.065 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/uploadTex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wsFooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.066 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/wseditor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.067 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.067 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.067 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandaCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bannerManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.068 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_loginChecker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.069 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.069 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.069 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubDesk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addNotesScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.070 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferenceSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.071 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.071 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookChaptersTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.071 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/indexHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.071 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_quizSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.072 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.072 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviews.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.072 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_flashCardSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.073 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/selfService.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.074 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerProcessor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.074 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyOrAdd.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.074 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/relatedVideosAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/testgenerator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.075 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discussform.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.076 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.076 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_uploadResourcesModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.076 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelectorScript.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_reviewrating.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.077 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discforum.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_slider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.078 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/independentContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.079 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulkinput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.079 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.079 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.080 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolderTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.080 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoplayer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.080 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/studySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.080 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.081 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/arihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.081 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/errorPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.081 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wonderGoaStudySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.082 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoPlay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.082 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoExplanation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.082 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.082 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.083 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.083 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readingSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.083 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.083 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookGPTTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.084 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandacreator.js] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.085 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/layouts/main.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.086 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/sales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.086 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.086 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/contentCreators.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.087 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.087 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.087 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/users.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.088 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.088 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.088 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/content.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.088 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.089 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/amazonLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.089 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.089 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/fixBrokenResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.090 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/progress/progressReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.090 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.091 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/_publishersList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.091 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.091 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/help.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.092 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.092 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.092 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.092 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.093 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.093 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.093 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.094 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.094 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.094 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/packages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.094 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/feedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.095 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.095 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.095 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.096 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/discussionBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.096 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/chapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.097 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/jsonPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.097 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.097 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/remote.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.097 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/_showResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.098 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/myTracker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.098 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.099 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/specimenCopyRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.099 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.099 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/favouriteMcqs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.099 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersNomineeForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.100 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersPollResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.100 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/addUserAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.100 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/_ordersListInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.101 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/editprofile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.101 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nominations.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.101 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nomineeDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.102 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/addPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.102 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisherReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.103 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.103 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/externalReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.104 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.104 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/instituteReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.104 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.104 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.105 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/getBulkUsersAddedReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.105 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/scratchCardReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.106 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeQuizzes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.106 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mcqSorter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.106 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/addNotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.106 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/sectionModifier.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.107 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/aiBookMigrate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.107 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/findParentBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.107 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeMCQInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.108 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/addBulkUsersAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.108 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/formulaFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.109 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/bookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.109 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/fileUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.109 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/_fileUploaderInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.109 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/imageFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.110 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/quizExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.110 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadMCQ.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.110 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/extractPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.111 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/upload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.111 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/htmlClassExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.111 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/wordUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.111 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/exportmcqspage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.112 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.112 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_instructorResourcesContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.113 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.113 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.113 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_userlogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.114 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.114 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.114 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.115 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.115 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.115 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.116 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.116 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.116 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.116 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.117 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.117 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_askAuthorModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.117 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/verification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.118 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/disciplines.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.118 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/doris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.118 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_additionalStudentInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.119 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.119 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.120 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.120 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/accessCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.121 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.121 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.121 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/excelUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.122 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.122 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.122 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/ebouquetReviewerLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.123 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/virtualLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.123 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.123 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.124 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.124 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/information.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.125 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.125 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_accessCodeLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.125 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.125 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.126 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/termsOfUse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.126 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.127 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.127 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/privacyPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.127 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/decision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.128 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/requestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.128 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/cookies.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.129 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printOrderManagement/orderDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.129 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.129 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_relatedBooksAndLeaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.130 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/listExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.130 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_topLevelTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.130 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/examPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.131 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_mockTestFaq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.131 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.132 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/getGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.132 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showBlogPages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.133 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.133 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/categoryManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.133 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/searchResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.136 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.136 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/bookgpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.137 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.137 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.137 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForResId.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.138 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.138 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.138 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/manualGpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.139 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.139 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.139 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/myDriveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.140 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.140 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.140 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.141 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.141 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.141 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_chatModule.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.142 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_codeRunner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.142 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptContentHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.143 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookmark/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.144 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.144 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/showCategory.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.144 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/_categorySection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.145 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/adminIndex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.145 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.146 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/promptTemplateManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.146 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/duplicateFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.146 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/kindleTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.146 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/autogptTask.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.147 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.147 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/autoGPTAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.148 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportByPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.148 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportForAccounts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.148 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/partner/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.149 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/_copilot.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.149 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.149 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/manageLinks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.150 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/_aira.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.150 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/pageInteractions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.150 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.151 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.151 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.151 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.152 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.152 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.152 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/benefits.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.152 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/features.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.153 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/printBookBundling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.153 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sdk/sdkIntegrationReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.154 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBatches.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.154 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listUsersInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.154 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/addInstitute.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.155 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.155 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/getBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.155 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/adminDashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.155 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.156 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBooksInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.156 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignUserToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.156 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignBookToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.157 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.157 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.157 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.157 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listCourses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.158 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.158 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/manageInstitutePrompts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.159 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/promptLanguages/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.159 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testQuestionAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.159 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/blockStudents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.160 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/listTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.160 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.160 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/viewQuestions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.161 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.161 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/_api_modal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.161 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/createSolution.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.161 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqTranslator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.162 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/getPdfFile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.162 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.162 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/cookieTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.163 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/createQuestionPaperPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.163 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listQuestionPapers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.163 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.164 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addQuestionType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.164 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewPattern.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.164 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listPatterns.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.165 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.165 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/printQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.165 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/aireport/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.166 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.166 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.166 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentCreation/getSolvedPaperList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.167 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.167 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/_rightContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.167 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/_leftContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.168 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/sample.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.168 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/chapterPdf.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.168 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/bookTitle.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.169 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/theoryChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.169 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/_theoryChapterContents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.169 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/theoryBooks/theoryBookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.170 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/liveMockTests/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.170 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/liveMockTests/manageLiveMockTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.171 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslate/LoginFilters.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.171 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/spring/resources.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:15.174 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp/views.properties] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
10-Sep-2025 06:38:25.736 INFO [ws-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
10-Sep-2025 06:38:28.123 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

Configuring Spring Security Core ...
... finished configuring Spring Security Core


Configuring Spring Security REST 2.0.0.M2...
... finished configuring Spring Security REST

	... with GORM support
10-Sep-2025 06:40:28.139 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deployment of configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml has finished in 148,673 ms
10-Sep-2025 06:40:28.150 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war
10-Sep-2025 06:40:32.125 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::        (v2.2.2.RELEASE)

2025-09-10 06:40:33.256  INFO 326988 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Starting ServletInitializer v0.0.1-SNAPSHOT on ip-172-31-30-27.us-west-2.compute.internal with PID 326988 (/u01/apache-tomcat-8.5.13/webapps/wonderlive/WEB-INF/classes started by root in /u01/apache-tomcat-8.5.13/bin)
2025-09-10 06:40:33.277  INFO 326988 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : No active profile set, falling back to default profiles: default
10-Sep-2025 06:40:34.943 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log Initializing Spring embedded WebApplicationContext
2025-09-10 06:40:34.945  INFO 326988 --- [ ws-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 1586 ms
2025-09-10 06:40:35.382  INFO 326988 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientInboundChannelExecutor'
2025-09-10 06:40:35.387  INFO 326988 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-09-10 06:40:35.434  INFO 326988 --- [ ws-startStop-1] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-09-10 06:40:35.494  INFO 326988 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'brokerChannelExecutor'
2025-09-10 06:40:36.449  WARN 326988 --- [ ws-startStop-1] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-09-10 06:40:36.706  INFO 326988 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Starting...
2025-09-10 06:40:36.706  INFO 326988 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-09-10 06:40:36.707  INFO 326988 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Started.
2025-09-10 06:40:36.725  INFO 326988 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Started ServletInitializer in 4.394 seconds (JVM running for 159.064)
10-Sep-2025 06:40:36.740 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war has finished in 8,590 ms
10-Sep-2025 06:40:36.741 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known
10-Sep-2025 06:40:36.754 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known has finished in 13 ms
10-Sep-2025 06:40:36.755 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/tools
10-Sep-2025 06:40:36.777 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/tools has finished in 21 ms
10-Sep-2025 06:40:36.791 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["ajp-nio-8009"]
10-Sep-2025 06:40:36.807 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 157407 ms
cs
10-Sep-2025 06:41:31.532 INFO [ajp-nio-8009-exec-1] org.apache.tomcat.util.http.parser.Cookie.logInvalidHeader A cookie header was received [Sep 10 2025 12:11:31 GMT+0530 (India Standard Time); pomodoroCurrentDuration=0; daysDuration=0] that contained an invalid cookie. That cookie will be ignored.Note: further occurrences of this error will be logged at DEBUG level.
2025-09-10 06:41:35.493  INFO 326988 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats    : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
coming here man for ogin
sql=select bm.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled,-1 batchId,bm.title, bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,bp.expiry_date,bm.package_book_ids,'' batch_name,bm.show_in_library,bp.package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.test_type_book  from books_mst bm, wsuser.books_permission bp where bp.book_id=bm.id  and  (bm.show_in_library='Yes' or bm.show_in_library is null)  and bp.username='71_8754178781' union select bm1.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId, bm1.title,bm1.book_type,bm1.test_start_date,bm1.test_end_date,bm1.cover_image,bm1.price,bm1.site_id,bp.expiry_date,bm1.package_book_ids,'' batch_name,bm1.show_in_library,bm.id package_book_id,bm1.publisher_id,bm1.book_type,bm.language,bm.test_type_book from  books_mst bm, books_permission bp, books_mst bm1  where  bm1.show_in_library='Yes'  and bp.username='71_8754178781' and bm.id=bp.book_id   and bm.package_book_ids is not null and FIND_IN_SET(bm1.id,bm.package_book_ids)!=0  union select bm.id,bm.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId, bm.title,bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,null expiry_date,bm.package_book_ids,'' batch_name,bm.show_in_library,null package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.test_type_book from  books_mst bm,wsshop.books_tag_dtl btd, wsshop.book_price_dtl bpd  where  bm.status='published' and btd.book_id=bm.id and bpd.book_id=bm.id    and  btd.syllabus='Current Affairs'  and bpd.sell_price=0 and bm.site_id=71 order by dateCreated desc
bookId in the beginning312785
queryBookId in the afterwards 312785
*** entered add quiz
*** entered add quiz 2 and mode is edit
*** entered add quiz 3
*** edit thingy started
bookId in the beginning312785
queryBookId in the afterwards 312785
*** total time

2025-09-10 06:43:40.467 ERROR --- [nio-8009-exec-7] g.a.c.c.w.data.LiveMockTestsController   : Error in addMCQsToMockTest: No such property: liveMockMst for class: com.wonderslate.data.LiveMockTestsController

groovy.lang.MissingPropertyException: No such property: liveMockMst for class: com.wonderslate.data.LiveMockTestsController
	at org.codehaus.groovy.runtime.ScriptBytecodeAdapter.unwrap(ScriptBytecodeAdapter.java:53)
	at org.codehaus.groovy.runtime.ScriptBytecodeAdapter.setGroovyObjectProperty(ScriptBytecodeAdapter.java:534)
	at com.wonderslate.data.LiveMockTestsController.$tt__addMCQsToMockTest(LiveMockTestsController.groovy:40)
	at com.wonderslate.data.LiveMockTestsController$_addMCQsToMockTest_closure1.doCall(LiveMockTestsController.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.LiveMockTestsController.addMCQsToMockTest(LiveMockTestsController.groovy)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:230)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at javax.servlet.FilterChain$doFilter$1.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.trace.WebRequestTraceFilter.doFilterInternal(WebRequestTraceFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter$1.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at sun.reflect.GeneratedMethodAccessor444.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:190)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter$1.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.autoconfigure.MetricsFilter.doFilterInternal(MetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:80)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:341)
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:486)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:861)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1455)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

